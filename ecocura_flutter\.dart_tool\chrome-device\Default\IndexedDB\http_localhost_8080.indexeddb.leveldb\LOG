2025/07/03-20:08:57.657 8068 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.3f057cc1\flutter_tools_chrome_device.1fbe2688\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/07/03-20:08:57.658 8068 Recovering log #50
2025/07/03-20:08:57.661 8068 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.3f057cc1\flutter_tools_chrome_device.1fbe2688\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000050.log 
2025/07/03-20:08:57.661 8068 Delete type=0 #4
2025/07/03-20:08:57.661 8068 Delete type=2 #5
2025/07/03-20:08:57.661 8068 Delete type=0 #11
2025/07/03-20:08:57.661 8068 Delete type=2 #13
2025/07/03-20:08:57.661 8068 Delete type=0 #15
2025/07/03-20:08:57.661 8068 Delete type=2 #17
2025/07/03-20:08:57.662 8068 Delete type=0 #23
2025/07/03-20:08:57.662 8068 Delete type=2 #25
2025/07/03-20:08:57.662 8068 Delete type=0 #27
2025/07/03-20:08:57.662 8068 Delete type=2 #29
2025/07/03-20:08:57.662 8068 Delete type=0 #31
2025/07/03-20:08:57.662 8068 Delete type=2 #33
2025/07/03-20:08:57.662 8068 Delete type=0 #35
2025/07/03-20:08:57.662 8068 Delete type=2 #37
2025/07/03-20:08:57.662 8068 Delete type=0 #39
2025/07/03-20:08:57.662 8068 Delete type=2 #41
2025/07/03-20:08:57.662 8068 Delete type=0 #46
2025/07/03-20:08:57.662 8068 Delete type=2 #48
2025/07/03-20:08:57.671 8ba8 Level-0 table #55: started
2025/07/03-20:08:57.678 8ba8 Level-0 table #55: 66070 bytes OK
2025/07/03-20:08:57.679 8ba8 Delete type=0 #50
2025/07/03-20:08:57.680 84f0 Manual compaction at level-0 from '\x00\x11\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x12\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/03-20:09:19.650 8068 Compacting 1@1 + 1@2 files
2025/07/03-20:09:19.654 8068 Generated table #56@1: 269 keys, 7595 bytes
2025/07/03-20:09:19.654 8068 Compacted 1@1 + 1@2 files => 7595 bytes
2025/07/03-20:09:19.657 8068 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/03-20:09:19.657 8068 Delete type=2 #52
2025/07/03-20:09:19.657 8068 Delete type=2 #55
