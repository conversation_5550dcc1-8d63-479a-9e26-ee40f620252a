2025/07/03-16:37:18.652 5624 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.ea5e2e00\flutter_tools_chrome_device.163dca3d\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/07/03-16:37:18.652 5624 Recovering log #35
2025/07/03-16:37:18.652 5624 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.ea5e2e00\flutter_tools_chrome_device.163dca3d\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000035.log 
2025/07/03-16:37:18.653 5624 Delete type=0 #4
2025/07/03-16:37:18.653 5624 Delete type=2 #5
2025/07/03-16:37:18.653 5624 Delete type=0 #11
2025/07/03-16:37:18.653 5624 Delete type=2 #13
2025/07/03-16:37:18.653 5624 Delete type=0 #15
2025/07/03-16:37:18.653 5624 Delete type=2 #17
2025/07/03-16:37:18.653 5624 Delete type=0 #23
2025/07/03-16:37:18.653 5624 Delete type=2 #25
2025/07/03-16:37:18.653 5624 Delete type=0 #27
2025/07/03-16:37:18.653 5624 Delete type=2 #29
2025/07/03-16:37:18.653 5624 Delete type=0 #31
2025/07/03-16:37:18.653 5624 Delete type=2 #33
2025/07/03-16:37:18.662 6a4 Level-0 table #40: started
2025/07/03-16:37:18.666 6a4 Level-0 table #40: 13821 bytes OK
2025/07/03-16:37:18.667 6a4 Delete type=0 #35
2025/07/03-16:37:18.667 7bf0 Manual compaction at level-0 from '\x00\x0d\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0e\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/03-16:37:48.806 5624 Compacting 1@1 + 1@2 files
2025/07/03-16:37:48.808 5624 Generated table #41@1: 260 keys, 7215 bytes
2025/07/03-16:37:48.809 5624 Compacted 1@1 + 1@2 files => 7215 bytes
2025/07/03-16:37:48.810 5624 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/03-16:37:48.810 5624 Delete type=2 #37
2025/07/03-16:37:48.810 5624 Delete type=2 #40
