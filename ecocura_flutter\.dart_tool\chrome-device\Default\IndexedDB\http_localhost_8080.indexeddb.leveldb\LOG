2025/07/03-13:53:28.109 3338 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.ab5e9613\flutter_tools_chrome_device.b0ab458d\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/07/03-13:53:28.110 3338 Recovering log #23
2025/07/03-13:53:28.111 3338 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.ab5e9613\flutter_tools_chrome_device.b0ab458d\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000023.log 
2025/07/03-13:53:28.111 3338 Delete type=0 #4
2025/07/03-13:53:28.111 3338 Delete type=2 #5
2025/07/03-13:53:28.111 3338 Delete type=0 #11
2025/07/03-13:53:28.111 3338 Delete type=2 #13
2025/07/03-13:53:28.111 3338 Delete type=0 #15
2025/07/03-13:53:28.111 3338 Delete type=2 #17
2025/07/03-13:53:28.117 66d4 Level-0 table #28: started
2025/07/03-13:53:28.120 66d4 Level-0 table #28: 23388 bytes OK
2025/07/03-13:53:28.121 66d4 Delete type=0 #23
2025/07/03-13:53:28.122 4f2c Manual compaction at level-0 from '\x00\x0a\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0b\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/03-13:53:50.740 66d4 Compacting 1@1 + 1@2 files
2025/07/03-13:53:50.743 66d4 Generated table #29@1: 266 keys, 7620 bytes
2025/07/03-13:53:50.743 66d4 Compacted 1@1 + 1@2 files => 7620 bytes
2025/07/03-13:53:50.744 66d4 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/03-13:53:50.745 66d4 Delete type=2 #25
2025/07/03-13:53:50.745 66d4 Delete type=2 #28
