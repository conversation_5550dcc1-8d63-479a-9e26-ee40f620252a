import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

class MLService {
  static List<String>? _labels;
  static bool _isInitialized = false;

  // Initialize the ML model (web-compatible)
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load labels
      final labelsData = await rootBundle.loadString('assets/ml_models/labels.txt');
      _labels = labelsData.split('\n').where((label) => label.isNotEmpty).toList();

      _isInitialized = true;
      if (kDebugMode) {
        print('ML Service initialized with mock data');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to initialize ML Service: $e');
      }
      _isInitialized = false;
    }
  }

  // Classify waste from image bytes (web-compatible with mock data)
  static Future<WasteDetectionResult> classifyWaste(Uint8List imageBytes) async {
    if (!_isInitialized) {
      await initialize();
    }

    // For web compatibility, always use mock results
    // On mobile platforms, you can integrate real TensorFlow Lite here
    if (kIsWeb) {
      return _getMockResult();
    }

    // For mobile platforms, you would implement real ML inference here
    // For now, using mock data for all platforms
    return _getMockResult();
  }



  // Get upcycling suggestions based on detected waste type
  static List<String> _getUpcyclingSuggestions(String wasteType) {
    final suggestions = {
      'plastic_bottle': [
        'Create a Bird Feeder - Cut holes and add perches',
        'Make a Planter - Perfect for herbs and small plants',
        'DIY Pen Holder - Organize your desk supplies',
      ],
      'cardboard_box': [
        'Build a Desk Organizer - Multiple compartments',
        'Create Storage Solutions - Decorative boxes',
        'Make a Cat House - Fun project for pets',
      ],
      'tin_can': [
        'Craft a Lantern - Add holes for light patterns',
        'Make a Pen Holder - Wrap with decorative paper',
        'Create a Planter - Great for succulents',
      ],
      'toilet_paper_roll': [
        'Organize Cables - Perfect cord management',
        'Make Seedling Pots - Biodegradable planters',
        'Create Bird Feeders - Cover with peanut butter',
      ],
      'fabric': [
        'Sew a Tote Bag - Reusable shopping bag',
        'Make Cleaning Rags - Cut into useful sizes',
        'Create Plant Ties - Soft support for plants',
      ],
    };

    return suggestions[wasteType.toLowerCase()] ?? [
      'Creative Decoration - Paint and personalize',
      'Storage Solution - Organize small items',
      'Garden Helper - Use in outdoor projects',
    ];
  }

  // Mock result for development/fallback
  static WasteDetectionResult _getMockResult() {
    final mockLabels = ['Plastic Bottle', 'Cardboard Box', 'Tin Can', 'Toilet Paper Roll', 'Fabric'];
    final randomLabel = mockLabels[DateTime.now().millisecond % mockLabels.length];

    return WasteDetectionResult(
      label: randomLabel,
      confidence: 0.85 + (DateTime.now().millisecond % 15) / 100, // Random confidence 0.85-0.99
      suggestions: _getUpcyclingSuggestions(randomLabel.toLowerCase().replaceAll(' ', '_')),
    );
  }

  static void dispose() {
    _labels = null;
    _isInitialized = false;
  }
}

class WasteDetectionResult {
  final String label;
  final double confidence;
  final List<String> suggestions;

  WasteDetectionResult({
    required this.label,
    required this.confidence,
    required this.suggestions,
  });
}
