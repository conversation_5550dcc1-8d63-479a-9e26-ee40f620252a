{"logs": [{"outputFile": "com.example.ecocura_flutter.app-mergeDebugResources-47:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,235,236,237,238,239,240,241,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,360,361,362,363,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,433,438,439,440,441,442,443,451,452,456,460,464,469,475,482,486,490,495,499,503,507,511,515,519,525,529,535,539,545,549,554,558,561,565,571,575,581,585,591,594,598,602,606,610,614,615,616,617,620,623,626,629,633,634,635,636,637,640,642,644,646,651,652,656,662,666,667,669,680,681,685,691,695,696,697,701,728,732,733,737,765,935,961,1132,1158,1189,1197,1203,1217,1239,1244,1249,1259,1268,1277,1281,1288,1296,1303,1304,1313,1316,1319,1323,1327,1331,1334,1335,1340,1345,1355,1360,1367,1373,1374,1377,1381,1386,1388,1390,1393,1396,1398,1402,1405,1412,1415,1418,1422,1424,1428,1430,1432,1434,1438,1446,1454,1466,1472,1481,1484,1495,1498,1499,1504,1505,1534,1603,1673,1674,1684,1693,1845,1847,1851,1854,1857,1860,1863,1866,1869,1872,1876,1879,1882,1885,1889,1892,1896,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1922,1924,1925,1926,1927,1928,1929,1930,1931,1933,1934,1936,1937,1939,1941,1942,1944,1945,1946,1947,1948,1949,1951,1952,1953,1954,1955,1967,1969,1971,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1985,1987,1988,1989,1990,1991,1992,1994,1998,2014,2015,2016,2017,2018,2019,2023,2024,2025,2026,2028,2030,2032,2034,2036,2037,2038,2039,2041,2043,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2056,2059,2060,2061,2062,2064,2066,2067,2069,2070,2072,2074,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2087,2089,2090,2091,2092,2094,2095,2096,2097,2098,2100,2102,2104,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2126,2201,2204,2207,2210,2224,2241,2283,2312,2339,2348,2410,2774,2805,2943,3067,3091,3097,3126,3147,3271,3299,3305,3449,3475,3542,3613,3713,3733,3788,3800,3826", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2138,2273,2354,2415,2490,2566,2643,2881,2966,3048,3124,3200,3277,3355,3461,3567,3646,3975,4032,4892,4966,5041,5106,5172,5232,5293,5365,5438,5505,5573,5632,5691,5750,5809,5868,5922,5976,6029,6083,6137,6191,6446,6520,6599,6672,6746,6817,6889,6961,7034,7091,7149,7222,7296,7370,7445,7517,7590,7660,7731,7791,7852,7921,7990,8060,8134,8210,8274,8351,8427,8504,8569,8638,8715,8790,8859,8927,9004,9070,9131,9228,9293,9362,9461,9532,9591,9649,9706,9765,9829,9900,9972,10044,10116,10188,10255,10323,10391,10450,10513,10577,10667,10758,10818,10884,10951,11017,11087,11151,11204,11271,11332,11399,11512,11570,11633,11698,11763,11838,11911,11983,12032,12093,12154,12215,12277,12341,12405,12469,12534,12597,12657,12718,12784,12843,12903,12965,13036,13096,13795,13881,14131,14221,14308,14396,14478,14561,14651,16376,16428,16486,16531,16597,16661,16718,16775,18952,19009,19057,19106,19361,19731,19778,20036,21207,21510,21574,21636,21696,22017,22091,22161,22239,22293,22363,22448,22496,22542,22603,22666,22732,22796,22867,22930,22995,23059,23120,23181,23233,23306,23380,23449,23524,23598,23672,23813,27538,27899,27977,28067,28155,28251,28341,28923,29012,29259,29540,29792,30077,30470,30947,31169,31391,31667,31894,32124,32354,32584,32814,33041,33460,33686,34111,34341,34769,34988,35271,35479,35610,35837,36263,36488,36915,37136,37561,37681,37957,38258,38582,38873,39187,39324,39455,39560,39802,39969,40173,40381,40652,40764,40876,40981,41098,41312,41458,41598,41684,42032,42120,42366,42784,43033,43115,43213,43805,43905,44157,44581,44836,44930,45019,45256,47280,47522,47624,47877,50033,60565,62081,72712,74240,75997,76623,77043,78104,79369,79625,79861,80408,80902,81507,81705,82285,82849,83224,83342,83880,84037,84233,84506,84762,84932,85073,85137,85502,85869,86545,86809,87147,87500,87594,87780,88086,88348,88473,88600,88839,89050,89169,89362,89539,89994,90175,90297,90556,90669,90856,90958,91065,91194,91469,91977,92473,93350,93644,94214,94363,95095,95267,95351,95687,95779,97845,103091,108480,108542,109120,109704,117651,117764,117993,118153,118305,118476,118642,118811,118978,119141,119384,119554,119727,119898,120172,120371,120576,120906,120990,121086,121182,121280,121380,121482,121584,121686,121788,121890,121990,122086,122198,122327,122450,122581,122712,122810,122924,123018,123158,123292,123388,123500,123600,123716,123812,123924,124024,124164,124300,124464,124594,124752,124902,125043,125187,125322,125434,125584,125712,125840,125976,126108,126238,126368,126480,127378,127524,127668,127806,127872,127962,128038,128142,128232,128334,128442,128550,128650,128730,128822,128920,129030,129108,129214,129306,129410,129520,129642,129805,130595,130675,130775,130865,130975,131065,131306,131400,131506,131598,131698,131810,131924,132040,132156,132250,132364,132476,132578,132698,132820,132902,133006,133126,133252,133350,133444,133532,133644,133760,133882,133994,134169,134285,134371,134463,134575,134699,134766,134892,134960,135088,135232,135360,135429,135524,135639,135752,135851,135960,136071,136182,136283,136388,136488,136618,136709,136832,136926,137038,137124,137228,137324,137412,137530,137634,137738,137864,137952,138060,138160,138250,138360,138444,138546,138630,138684,138748,138854,138940,139050,139134,139538,142154,142272,142387,142467,142828,143414,144818,146162,147523,147911,150686,160775,161815,168628,172929,173680,173942,174789,175168,179446,180300,180529,185137,186147,188099,190499,194623,195367,197498,197838,199149", "endLines": "4,27,28,59,60,61,62,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,230,231,235,236,237,238,239,240,241,267,268,269,270,271,272,273,274,310,311,312,313,318,326,327,332,354,360,361,362,363,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,433,438,439,440,441,442,450,451,455,459,463,468,474,481,485,489,494,498,502,506,510,514,518,524,528,534,538,544,548,553,557,560,564,570,574,580,584,590,593,597,601,605,609,613,614,615,616,619,622,625,628,632,633,634,635,636,639,641,643,645,650,651,655,661,665,666,668,679,680,684,690,694,695,696,700,727,731,732,736,764,934,960,1131,1157,1188,1196,1202,1216,1238,1243,1248,1258,1267,1276,1280,1287,1295,1302,1303,1312,1315,1318,1322,1326,1330,1333,1334,1339,1344,1354,1359,1366,1372,1373,1376,1380,1385,1387,1389,1392,1395,1397,1401,1404,1411,1414,1417,1421,1423,1427,1429,1431,1433,1437,1445,1453,1465,1471,1480,1483,1494,1497,1498,1503,1504,1509,1602,1672,1673,1683,1692,1693,1846,1850,1853,1856,1859,1862,1865,1868,1871,1875,1878,1881,1884,1888,1891,1895,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1910,1911,1912,1913,1914,1915,1916,1917,1918,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1932,1933,1935,1936,1938,1940,1941,1943,1944,1945,1946,1947,1948,1950,1951,1952,1953,1954,1955,1968,1970,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1983,1984,1986,1987,1988,1989,1990,1991,1993,1997,2001,2014,2015,2016,2017,2018,2022,2023,2024,2025,2027,2029,2031,2033,2035,2036,2037,2038,2040,2042,2044,2045,2046,2047,2048,2049,2050,2051,2052,2053,2054,2055,2058,2059,2060,2061,2063,2065,2066,2068,2069,2071,2073,2075,2076,2077,2078,2079,2080,2081,2082,2083,2084,2085,2086,2088,2089,2090,2091,2093,2094,2095,2096,2097,2099,2101,2103,2105,2106,2107,2108,2109,2110,2111,2112,2113,2114,2115,2116,2117,2118,2119,2120,2200,2203,2206,2209,2223,2229,2250,2311,2338,2347,2409,2768,2777,2832,2960,3090,3096,3102,3146,3270,3290,3304,3308,3454,3509,3553,3678,3732,3787,3799,3825,3832", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2133,2195,2349,2410,2485,2561,2638,2716,2961,3043,3119,3195,3272,3350,3456,3562,3641,3721,4027,4085,4961,5036,5101,5167,5227,5288,5360,5433,5500,5568,5627,5686,5745,5804,5863,5917,5971,6024,6078,6132,6186,6240,6515,6594,6667,6741,6812,6884,6956,7029,7086,7144,7217,7291,7365,7440,7512,7585,7655,7726,7786,7847,7916,7985,8055,8129,8205,8269,8346,8422,8499,8564,8633,8710,8785,8854,8922,8999,9065,9126,9223,9288,9357,9456,9527,9586,9644,9701,9760,9824,9895,9967,10039,10111,10183,10250,10318,10386,10445,10508,10572,10662,10753,10813,10879,10946,11012,11082,11146,11199,11266,11327,11394,11507,11565,11628,11693,11758,11833,11906,11978,12027,12088,12149,12210,12272,12336,12400,12464,12529,12592,12652,12713,12779,12838,12898,12960,13031,13091,13159,13876,13963,14216,14303,14391,14473,14556,14646,14737,16423,16481,16526,16592,16656,16713,16770,16824,19004,19052,19101,19152,19390,19773,19822,20077,21234,21569,21631,21691,21748,22086,22156,22234,22288,22358,22443,22491,22537,22598,22661,22727,22791,22862,22925,22990,23054,23115,23176,23228,23301,23375,23444,23519,23593,23667,23808,23878,27586,27972,28062,28150,28246,28336,28918,29007,29254,29535,29787,30072,30465,30942,31164,31386,31662,31889,32119,32349,32579,32809,33036,33455,33681,34106,34336,34764,34983,35266,35474,35605,35832,36258,36483,36910,37131,37556,37676,37952,38253,38577,38868,39182,39319,39450,39555,39797,39964,40168,40376,40647,40759,40871,40976,41093,41307,41453,41593,41679,42027,42115,42361,42779,43028,43110,43208,43800,43900,44152,44576,44831,44925,45014,45251,47275,47517,47619,47872,50028,60560,62076,72707,74235,75992,76618,77038,78099,79364,79620,79856,80403,80897,81502,81700,82280,82844,83219,83337,83875,84032,84228,84501,84757,84927,85068,85132,85497,85864,86540,86804,87142,87495,87589,87775,88081,88343,88468,88595,88834,89045,89164,89357,89534,89989,90170,90292,90551,90664,90851,90953,91060,91189,91464,91972,92468,93345,93639,94209,94358,95090,95262,95346,95682,95774,96052,103086,108475,108537,109115,109699,109790,117759,117988,118148,118300,118471,118637,118806,118973,119136,119379,119549,119722,119893,120167,120366,120571,120901,120985,121081,121177,121275,121375,121477,121579,121681,121783,121885,121985,122081,122193,122322,122445,122576,122707,122805,122919,123013,123153,123287,123383,123495,123595,123711,123807,123919,124019,124159,124295,124459,124589,124747,124897,125038,125182,125317,125429,125579,125707,125835,125971,126103,126233,126363,126475,126615,127519,127663,127801,127867,127957,128033,128137,128227,128329,128437,128545,128645,128725,128817,128915,129025,129103,129209,129301,129405,129515,129637,129800,129957,130670,130770,130860,130970,131060,131301,131395,131501,131593,131693,131805,131919,132035,132151,132245,132359,132471,132573,132693,132815,132897,133001,133121,133247,133345,133439,133527,133639,133755,133877,133989,134164,134280,134366,134458,134570,134694,134761,134887,134955,135083,135227,135355,135424,135519,135634,135747,135846,135955,136066,136177,136278,136383,136483,136613,136704,136827,136921,137033,137119,137223,137319,137407,137525,137629,137733,137859,137947,138055,138155,138245,138355,138439,138541,138625,138679,138743,138849,138935,139045,139129,139249,142149,142267,142382,142462,142823,143056,143926,146157,147518,147906,150681,160585,160905,163167,169195,173675,173937,174137,175163,179441,180047,180524,180675,185347,187225,188406,193520,195362,197493,197833,199144,199347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e93556932885008eff7df21847fbdad2\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2251,2267,2273,3593,3609", "startColumns": "4,4,4,4,4", "startOffsets": "143931,144356,144534,189961,190372", "endLines": "2266,2272,2282,3608,3612", "endColumns": "24,24,24,24,24", "endOffsets": "144351,144529,144813,190367,190494"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,120,121,223,224,225,226,227,228,229,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,367,398,399,400,401,402,403,404,434,1956,1957,1961,1962,1966,2121,2122,2778,2795,2965,2998,3028,3061", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2721,2793,4090,4155,6245,6314,13307,13377,13445,13517,13587,13648,13722,14965,15026,15087,15149,15213,15275,15336,15404,15504,15564,15630,15703,15772,15829,15881,16829,16901,16977,17042,17101,17160,17220,17280,17340,17400,17460,17520,17580,17640,17700,17760,17819,17879,17939,17999,18059,18119,18179,18239,18299,18359,18419,18478,18538,18598,18657,18716,18775,18834,18893,19461,19496,20082,20137,20200,20255,20313,20371,20432,20495,20552,20603,20653,20714,20771,20837,20871,20906,21947,24132,24199,24271,24340,24409,24483,24555,27591,126620,126737,126938,127048,127249,139254,139326,160910,161514,169349,171080,172080,172762", "endLines": "29,70,71,88,89,120,121,223,224,225,226,227,228,229,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,320,321,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,367,398,399,400,401,402,403,404,434,1956,1960,1961,1965,1966,2121,2122,2783,2804,2997,3018,3060,3066", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2788,2876,4150,4216,6309,6372,13372,13440,13512,13582,13643,13717,13790,15021,15082,15144,15208,15270,15331,15399,15499,15559,15625,15698,15767,15824,15876,15938,16896,16972,17037,17096,17155,17215,17275,17335,17395,17455,17515,17575,17635,17695,17755,17814,17874,17934,17994,18054,18114,18174,18234,18294,18354,18414,18473,18533,18593,18652,18711,18770,18829,18888,18947,19491,19526,20132,20195,20250,20308,20366,20427,20490,20547,20598,20648,20709,20766,20832,20866,20901,20936,22012,24194,24266,24335,24404,24478,24550,24638,27657,126732,126933,127043,127244,127373,139321,139388,161108,161810,171075,171756,172757,172924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5472a5df6f2e4e509470c3c4185f3d50\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2002", "startColumns": "4", "startOffsets": "129962", "endLines": "2005", "endColumns": "12", "endOffsets": "130180"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1c8746a36ac065afed39d95b2852a559\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "315,331,359,3019,3024", "startColumns": "4,4,4,4,4", "startOffsets": "19217,19971,21446,171761,171931", "endLines": "315,331,359,3023,3027", "endColumns": "56,64,63,24,24", "endOffsets": "19269,20031,21505,171926,172075"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\84addddb59162e1cea52976d5f2c6cc1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "358", "startColumns": "4", "startOffsets": "21396", "endColumns": "49", "endOffsets": "21441"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7976d4e64729cb9c47971e21b0850b04\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,405,406,407,408,409,410,411,412,414,415,416,417,418,419,420,421,422,3113,3523", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4221,4311,4391,4481,4571,4651,4732,4812,24643,24748,24929,25054,25161,25341,25464,25580,25850,26038,26143,26324,26449,26624,26772,26835,26897,174474,187682", "endLines": "90,91,92,93,94,95,96,97,405,406,407,408,409,410,411,412,414,415,416,417,418,419,420,421,422,3125,3541", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4306,4386,4476,4566,4646,4727,4807,4887,24743,24924,25049,25156,25336,25459,25575,25678,26033,26138,26319,26444,26619,26767,26830,26892,26971,174784,188094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aa55b2079cbc673a6a445c1850daa153\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "355", "startColumns": "4", "startOffsets": "21239", "endColumns": "42", "endOffsets": "21277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1186d146da5ef23629d7bf94e5a0d382\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "364,413", "startColumns": "4,4", "startOffsets": "21753,25683", "endColumns": "67,166", "endOffsets": "21816,25845"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\197f12b192a3f06912c946d4cbd2dd7d\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,314,2230,2236,3554,3562,3577", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,19157,143061,143256,188411,188693,189307", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,314,2235,2240,3561,3576,3592", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,19212,143251,143409,188688,189302,189956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f734b899c9b5bcf473e5c8a79b68b93\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "357", "startColumns": "4", "startOffsets": "21342", "endColumns": "53", "endOffsets": "21391"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0c69679757972620720ec039d7103818\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,221,222,424,426,427,428", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3726,3784,3850,3913,13164,13235,27014,27139,27206,27285", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3779,3845,3908,3970,13230,13302,27077,27201,27280,27349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\3848899b7e93201e983882e2ba4294f0\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "395,396", "startColumns": "4,4", "startOffsets": "23883,23965", "endColumns": "81,83", "endOffsets": "23960,24044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\85879f220671a879b538e8ef16ed1744\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "397", "startColumns": "4", "startOffsets": "24049", "endColumns": "82", "endOffsets": "24127"}}, {"source": "D:\\Code Bharat\\ecocura_flutter\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1527,1531", "startColumns": "4,4", "startOffsets": "97495,97676", "endLines": "1530,1533", "endColumns": "12,12", "endOffsets": "97671,97840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f87704cc6ac259b753f491455f413615\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "316,317,322,329,330,349,350,351,352,353", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "19274,19314,19531,19869,19924,20941,20995,21047,21096,21157", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "19309,19356,19569,19919,19966,20990,21042,21091,21152,21202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\5dd3740d4798cd744da95fbad85bd5d6\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2006", "startColumns": "4", "startOffsets": "130185", "endLines": "2013", "endColumns": "8", "endOffsets": "130590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\93eeca70efd8419049cd49df8af72af1\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "328,356", "startColumns": "4,4", "startOffsets": "19827,21282", "endColumns": "41,59", "endOffsets": "19864,21337"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\28f988f0d4c2cc22199e4c3cefdd595e\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2123,2833,2839", "startColumns": "4,4,4,4", "startOffsets": "164,139393,163172,163383", "endLines": "3,2125,2838,2922", "endColumns": "60,12,24,24", "endOffsets": "220,139533,163378,167894"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "63,122,260,261,262,263,264,265,266,323,324,325,365,366,423,425,429,430,435,436,437,1510,1694,1697,1703,1709,1712,1718,1722,1725,1732,1738,1741,1747,1752,1757,1764,1766,1772,1778,1786,1791,1798,1803,1809,1813,1820,1824,1830,1836,1839,1843,1844,2769,2784,2923,2961,3103,3291,3309,3373,3383,3393,3400,3406,3510,3679,3696", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2200,6377,15943,16007,16062,16130,16197,16262,16319,19574,19622,19670,21821,21884,26976,27082,27354,27398,27662,27801,27851,96057,109795,109900,110145,110483,110629,110969,111181,111344,111751,112089,112212,112551,112790,113047,113418,113478,113816,114102,114551,114843,115231,115536,115880,116125,116455,116662,116930,117203,117347,117548,117595,160590,161113,167899,169200,174142,180052,180680,182605,182887,183192,183454,183714,187230,193525,194055", "endLines": "63,122,260,261,262,263,264,265,266,323,324,325,365,366,423,425,429,432,435,436,437,1526,1696,1702,1708,1711,1717,1721,1724,1731,1737,1740,1746,1751,1756,1763,1765,1771,1777,1785,1790,1797,1802,1808,1812,1819,1823,1829,1835,1838,1842,1843,1844,2773,2794,2942,2964,3112,3298,3372,3382,3392,3399,3405,3448,3522,3695,3712", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2268,6441,16002,16057,16125,16192,16257,16314,16371,19617,19665,19726,21879,21942,27009,27134,27393,27533,27796,27846,27894,97490,109895,110140,110478,110624,110964,111176,111339,111746,112084,112207,112546,112785,113042,113413,113473,113811,114097,114546,114838,115226,115531,115875,116120,116450,116657,116925,117198,117342,117543,117590,117646,160770,161509,168623,169344,174469,180295,182600,182882,183187,183449,183709,185132,187677,194050,194618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\79275990ee9dddfd68bc7c9d7157e0cd\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "232,233,234,242,243,244,319,3455", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "13968,14027,14075,14742,14817,14893,19395,185352", "endLines": "232,233,234,242,243,244,319,3474", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "14022,14070,14126,14812,14888,14960,19456,186142"}}]}]}