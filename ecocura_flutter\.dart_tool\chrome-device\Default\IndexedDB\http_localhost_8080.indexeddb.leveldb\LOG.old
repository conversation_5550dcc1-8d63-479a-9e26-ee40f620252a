2025/07/03-13:25:25.750 1260 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d08b6908\flutter_tools_chrome_device.e51cd011\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/07/03-13:25:25.750 1260 Recovering log #15
2025/07/03-13:25:25.750 1260 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.d08b6908\flutter_tools_chrome_device.e51cd011\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000015.log 
2025/07/03-13:25:25.751 1260 Delete type=0 #4
2025/07/03-13:25:25.751 1260 Delete type=2 #5
2025/07/03-13:25:25.751 1260 Delete type=0 #11
2025/07/03-13:25:25.751 1260 Delete type=2 #13
2025/07/03-13:25:25.757 2850 Level-0 table #20: started
2025/07/03-13:25:25.761 2850 Level-0 table #20: 11918 bytes OK
2025/07/03-13:25:25.762 2850 Delete type=0 #15
2025/07/03-13:25:25.763 1518 Manual compaction at level-0 from '\x00\x08\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x09\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/03-13:25:47.875 2850 Compacting 1@1 + 1@2 files
2025/07/03-13:25:47.879 2850 Generated table #21@1: 266 keys, 7606 bytes
2025/07/03-13:25:47.879 2850 Compacted 1@1 + 1@2 files => 7606 bytes
2025/07/03-13:25:47.880 2850 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/03-13:25:47.880 2850 Delete type=2 #17
2025/07/03-13:25:47.880 2850 Delete type=2 #20
