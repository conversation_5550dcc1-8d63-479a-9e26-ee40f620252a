2025/07/03-16:33:18.998 3a50 Reusing MANIFEST C:\Users\<USER>\AppData\Local\Temp\flutter_tools.bf7f126a\flutter_tools_chrome_device.135e296d\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/MANIFEST-000001
2025/07/03-16:33:18.998 3a50 Recovering log #31
2025/07/03-16:33:18.999 3a50 Reusing old log C:\Users\<USER>\AppData\Local\Temp\flutter_tools.bf7f126a\flutter_tools_chrome_device.135e296d\Default\IndexedDB\http_localhost_8080.indexeddb.leveldb/000031.log 
2025/07/03-16:33:18.999 3a50 Delete type=0 #4
2025/07/03-16:33:18.999 3a50 Delete type=2 #5
2025/07/03-16:33:18.999 3a50 Delete type=0 #11
2025/07/03-16:33:18.999 3a50 Delete type=2 #13
2025/07/03-16:33:18.999 3a50 Delete type=0 #15
2025/07/03-16:33:18.999 3a50 Delete type=2 #17
2025/07/03-16:33:18.999 3a50 Delete type=0 #23
2025/07/03-16:33:18.999 3a50 Delete type=2 #25
2025/07/03-16:33:18.999 3a50 Delete type=0 #27
2025/07/03-16:33:18.999 3a50 Delete type=2 #29
2025/07/03-16:33:19.009 d84 Level-0 table #36: started
2025/07/03-16:33:19.012 d84 Level-0 table #36: 35888 bytes OK
2025/07/03-16:33:19.013 d84 Delete type=0 #31
2025/07/03-16:33:19.013 8a9c Manual compaction at level-0 from '\x00\x0c\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x0d\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/07/03-16:33:40.624 4404 Compacting 1@1 + 1@2 files
2025/07/03-16:33:40.628 4404 Generated table #37@1: 266 keys, 7628 bytes
2025/07/03-16:33:40.628 4404 Compacted 1@1 + 1@2 files => 7628 bytes
2025/07/03-16:33:40.629 4404 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/07/03-16:33:40.630 4404 Delete type=2 #33
2025/07/03-16:33:40.630 4404 Delete type=2 #36
